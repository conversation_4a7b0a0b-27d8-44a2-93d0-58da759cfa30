<?php
/**
 * Database Migration Script
 * Adds external_id and external_source columns to recommendations table
 */

require_once 'config.php';
require_once 'includes/db_connect.php';

echo "<h2>MoodifyMe Database Migration</h2>";
echo "<p>Adding external_id and external_source columns to recommendations table...</p>";

try {
    // Check if columns already exist
    $result = $conn->query("DESCRIBE recommendations");
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    echo "<h3>Current table structure:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    $needsUpdate = false;
    
    // Add external_id column if it doesn't exist
    if (!in_array('external_id', $columns)) {
        echo "<p>Adding external_id column...</p>";
        $sql = "ALTER TABLE recommendations ADD COLUMN external_id VARCHAR(100) NULL AFTER link";
        if ($conn->query($sql)) {
            echo "<p>✅ external_id column added successfully</p>";
            $needsUpdate = true;
        } else {
            echo "<p>❌ Error adding external_id column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>✅ external_id column already exists</p>";
    }
    
    // Add external_source column if it doesn't exist
    if (!in_array('external_source', $columns)) {
        echo "<p>Adding external_source column...</p>";
        $sql = "ALTER TABLE recommendations ADD COLUMN external_source VARCHAR(50) NULL AFTER external_id";
        if ($conn->query($sql)) {
            echo "<p>✅ external_source column added successfully</p>";
            $needsUpdate = true;
        } else {
            echo "<p>❌ Error adding external_source column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>✅ external_source column already exists</p>";
    }
    
    // Add index for better performance
    echo "<p>Adding index for external fields...</p>";
    $sql = "ALTER TABLE recommendations ADD INDEX idx_external_recipe (external_id, external_source)";
    if ($conn->query($sql)) {
        echo "<p>✅ Index added successfully</p>";
    } else {
        // Index might already exist, check if it's a duplicate key error
        if (strpos($conn->error, 'Duplicate key name') !== false) {
            echo "<p>✅ Index already exists</p>";
        } else {
            echo "<p>⚠️ Warning adding index: " . $conn->error . "</p>";
        }
    }
    
    // Show updated table structure
    echo "<h3>Updated table structure:</h3>";
    $result = $conn->query("DESCRIBE recommendations");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($needsUpdate) {
        echo "<h3>🎉 Migration completed successfully!</h3>";
        echo "<p>You can now test the African meals API again:</p>";
        echo "<p><a href='api/populate_african_meals.php?action=fetch_and_populate&api=spoonacular&cuisine=african&limit=5' target='_blank'>Test API</a></p>";
    } else {
        echo "<h3>✅ Database is already up to date!</h3>";
        echo "<p>You can test the African meals API:</p>";
        echo "<p><a href='api/populate_african_meals.php?action=fetch_and_populate&api=spoonacular&cuisine=african&limit=5' target='_blank'>Test API</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error during migration: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='pages/african_meals_manager.php'>Go to African Meals Manager</a></p>";
echo "<p><a href='pages/dashboard.php'>Go to Dashboard</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h3 {
    color: #555;
    margin-top: 30px;
}

table {
    width: 100%;
    font-size: 14px;
}

th {
    background-color: #f8f9fa;
    padding: 8px;
    text-align: left;
}

td {
    padding: 6px 8px;
    border-bottom: 1px solid #ddd;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
