<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download MediaPipe Files for MoodifyMe</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .file-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .download-btn {
            background: #E55100;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background: #D32F2F;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 MediaPipe Setup for MoodifyMe</h1>
        
        <div class="status info">
            <strong>Instructions:</strong> Click each download button to get the required MediaPipe files for facial landmark emotion detection.
        </div>

        <div class="file-section">
            <h3>📚 Core MediaPipe Libraries</h3>
            <p>These are the main JavaScript libraries needed:</p>
            
            <a href="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/face_mesh.js" 
               class="download-btn" download="face_mesh.js">
                📥 Download face_mesh.js
            </a>
            
            <a href="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils@0.3.1640029074/camera_utils.js" 
               class="download-btn" download="camera_utils.js">
                📥 Download camera_utils.js
            </a>
            
            <a href="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils@0.3.1620248257/drawing_utils.js" 
               class="download-btn" download="drawing_utils.js">
                📥 Download drawing_utils.js
            </a>
        </div>

        <div class="file-section">
            <h3>🧠 Model Files (WASM)</h3>
            <p>These are the AI model files for face detection:</p>
            
            <a href="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/face_mesh_solution_packed_assets.data" 
               class="download-btn" download="face_mesh_solution_packed_assets.data">
                📥 Download Model Data
            </a>
            
            <a href="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/face_mesh_solution_simd_wasm_bin.wasm" 
               class="download-btn" download="face_mesh_solution_simd_wasm_bin.wasm">
                📥 Download SIMD WASM
            </a>
            
            <a href="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh@0.4.1633559619/face_mesh_solution_wasm_bin.wasm" 
               class="download-btn" download="face_mesh_solution_wasm_bin.wasm">
                📥 Download WASM Binary
            </a>
        </div>

        <div class="file-section">
            <h3>📁 File Organization</h3>
            <p>After downloading, organize files like this:</p>
            <pre>
MoodifyMe/
├── assets/
│   ├── js/
│   │   ├── mediapipe/
│   │   │   ├── face_mesh.js
│   │   │   ├── camera_utils.js
│   │   │   └── drawing_utils.js
│   │   └── landmark-emotion-detector.js (we'll create this)
│   └── models/
│       ├── face_mesh_solution_packed_assets.data
│       ├── face_mesh_solution_simd_wasm_bin.wasm
│       └── face_mesh_solution_wasm_bin.wasm
            </pre>
        </div>

        <div class="file-section">
            <h3>🚀 Next Steps</h3>
            <ol>
                <li>Download all files above</li>
                <li>Create the folder structure shown</li>
                <li>Place files in their respective folders</li>
                <li>I'll help you create the emotion detection code</li>
            </ol>
        </div>

        <div class="status success">
            <strong>Ready?</strong> Once you've downloaded and organized the files, let me know and I'll create the landmark emotion detection system!
        </div>
    </div>

    <script>
        // Track downloads
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Downloading:', this.textContent);
                // You could add download tracking here
            });
        });
    </script>
</body>
</html>
