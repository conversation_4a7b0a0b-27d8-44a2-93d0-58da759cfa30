<?php
/**
 * MoodifyMe - African Meals Database Manager
 * Interface for populating the database with real African meal data from external APIs
 */

// Include configuration and functions
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/db_connect.php';

// Start session
session_start();

// Check if user is logged in (you might want to add admin role checking)
if (!isset($_SESSION['user_id'])) {
    redirect(APP_URL . '/pages/login.php');
}

// Include header
include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        African Meals Database Manager
                    </h2>
                    <p class="card-text mt-2">Populate your database with real African meal data from external APIs</p>
                </div>
                <div class="card-body">
                    
                    <!-- API Status Section -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>API Configuration Status</h5>
                                </div>
                                <div class="card-body">
                                    <div id="apiStatus">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Checking API status...</span>
                                            </div>
                                            <p class="mt-2">Checking API configuration...</p>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-outline-primary" onclick="testApiConnections()">
                                            <i class="fas fa-plug me-2"></i>Test API Connections
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-magic fa-3x text-primary mb-3"></i>
                                    <h5>Auto-Populate Popular Dishes</h5>
                                    <p class="text-muted">Automatically fetch and add popular African dishes</p>
                                    <button type="button" class="btn btn-primary" onclick="populatePopularDishes()">
                                        <i class="fas fa-download me-2"></i>Start Auto-Population
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-search fa-3x text-success mb-3"></i>
                                    <h5>Search Specific Dish</h5>
                                    <p class="text-muted">Search for and add a specific African dish</p>
                                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#searchDishModal">
                                        <i class="fas fa-search me-2"></i>Search Dish
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-globe-africa fa-3x text-warning mb-3"></i>
                                    <h5>Fetch by Cuisine</h5>
                                    <p class="text-muted">Fetch recipes from specific African cuisines</p>
                                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#fetchCuisineModal">
                                        <i class="fas fa-utensils me-2"></i>Fetch Cuisine
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Database Stats -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Current Database Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div id="databaseStats">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading stats...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Activity Log -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Activity Log</h5>
                                </div>
                                <div class="card-body">
                                    <div id="activityLog">
                                        <p class="text-muted">No activities yet. Start by populating some recipes!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Dish Modal -->
<div class="modal fade" id="searchDishModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Search for Specific Dish</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="searchDishForm">
                    <div class="mb-3">
                        <label for="dishName" class="form-label">Dish Name</label>
                        <input type="text" class="form-control" id="dishName" placeholder="e.g., Jollof Rice, Tagine, Injera" required>
                        <div class="form-text">Enter the name of an African dish you want to add to the database</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="searchApi" class="form-label">API Source</label>
                        <select class="form-select" id="searchApi">
                            <option value="spoonacular">Spoonacular</option>
                            <option value="edamam">Edamam</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="searchSourceEmotion" class="form-label">Source Emotion</label>
                            <select class="form-select" id="searchSourceEmotion">
                                <option value="">Auto-detect</option>
                                <option value="sad">Sad</option>
                                <option value="angry">Angry</option>
                                <option value="anxious">Anxious</option>
                                <option value="stressed">Stressed</option>
                                <option value="bored">Bored</option>
                                <option value="tired">Tired</option>
                                <option value="neutral">Neutral</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="searchTargetEmotion" class="form-label">Target Emotion</label>
                            <select class="form-select" id="searchTargetEmotion">
                                <option value="">Auto-detect</option>
                                <option value="happy">Happy</option>
                                <option value="calm">Calm</option>
                                <option value="excited">Excited</option>
                                <option value="energetic">Energetic</option>
                                <option value="focused">Focused</option>
                                <option value="relaxed">Relaxed</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="searchAndAddDish()">
                    <i class="fas fa-search me-2"></i>Search & Add
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Fetch Cuisine Modal -->
<div class="modal fade" id="fetchCuisineModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fetch Recipes by Cuisine</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="fetchCuisineForm">
                    <div class="mb-3">
                        <label for="cuisineType" class="form-label">Cuisine Type</label>
                        <select class="form-select" id="cuisineType">
                            <option value="african">General African</option>
                            <option value="moroccan">Moroccan</option>
                            <option value="ethiopian">Ethiopian</option>
                            <option value="middle eastern">Middle Eastern</option>
                            <option value="mediterranean">Mediterranean</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fetchApi" class="form-label">API Source</label>
                        <select class="form-select" id="fetchApi">
                            <option value="spoonacular">Spoonacular</option>
                            <option value="edamam">Edamam</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fetchLimit" class="form-label">Number of Recipes</label>
                        <select class="form-select" id="fetchLimit">
                            <option value="5">5 recipes</option>
                            <option value="10">10 recipes</option>
                            <option value="20">20 recipes</option>
                            <option value="30">30 recipes</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="fetchSourceEmotion" class="form-label">Source Emotion</label>
                            <select class="form-select" id="fetchSourceEmotion">
                                <option value="">Auto-detect</option>
                                <option value="sad">Sad</option>
                                <option value="angry">Angry</option>
                                <option value="anxious">Anxious</option>
                                <option value="stressed">Stressed</option>
                                <option value="bored">Bored</option>
                                <option value="tired">Tired</option>
                                <option value="neutral">Neutral</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="fetchTargetEmotion" class="form-label">Target Emotion</label>
                            <select class="form-select" id="fetchTargetEmotion">
                                <option value="">Auto-detect</option>
                                <option value="happy">Happy</option>
                                <option value="calm">Calm</option>
                                <option value="excited">Excited</option>
                                <option value="energetic">Energetic</option>
                                <option value="focused">Focused</option>
                                <option value="relaxed">Relaxed</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="fetchByCuisine()">
                    <i class="fas fa-download me-2"></i>Fetch Recipes
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    loadApiStatus();
    loadDatabaseStats();
});

function loadApiStatus() {
    fetch('<?php echo APP_URL; ?>/api/populate_african_meals.php?action=get_api_status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayApiStatus(data.data);
            }
        })
        .catch(error => {
            document.getElementById('apiStatus').innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error:</strong> Failed to load API status
                </div>
            `;
        });
}

function displayApiStatus(status) {
    const statusHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="card ${status.spoonacular.configured ? 'border-success' : 'border-warning'}">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-utensils me-2"></i>Spoonacular API
                            ${status.spoonacular.configured ? '<span class="badge bg-success">Configured</span>' : '<span class="badge bg-warning">Not Configured</span>'}
                        </h6>
                        <p class="card-text small">
                            API Key: ${status.spoonacular.api_key_set ? '✅ Set' : '❌ Not Set'}<br>
                            Endpoint: ${status.spoonacular.endpoint}
                        </p>
                        ${!status.spoonacular.configured ? '<small class="text-muted">Update SPOONACULAR_API_KEY in includes/external_recipe_apis.php</small>' : ''}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card ${status.edamam.configured ? 'border-success' : 'border-warning'}">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-leaf me-2"></i>Edamam API
                            ${status.edamam.configured ? '<span class="badge bg-success">Configured</span>' : '<span class="badge bg-warning">Not Configured</span>'}
                        </h6>
                        <p class="card-text small">
                            App ID: ${status.edamam.app_id_set ? '✅ Set' : '❌ Not Set'}<br>
                            App Key: ${status.edamam.app_key_set ? '✅ Set' : '❌ Not Set'}<br>
                            Endpoint: ${status.edamam.endpoint}
                        </p>
                        ${!status.edamam.configured ? '<small class="text-muted">Update EDAMAM credentials in includes/external_recipe_apis.php</small>' : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('apiStatus').innerHTML = statusHtml;
}

function testApiConnections() {
    const button = event.target;
    const originalHtml = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Testing...';
    button.disabled = true;
    
    fetch('<?php echo APP_URL; ?>/api/populate_african_meals.php?action=test_api_connection&api=both')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let resultsHtml = '<div class="mt-3"><h6>Connection Test Results:</h6>';
                
                for (const [api, result] of Object.entries(data.data)) {
                    const statusClass = result.status === 'connected' ? 'success' : 'danger';
                    resultsHtml += `
                        <div class="alert alert-${statusClass} py-2">
                            <strong>${api.charAt(0).toUpperCase() + api.slice(1)}:</strong> ${result.message}
                        </div>
                    `;
                }
                
                resultsHtml += '</div>';
                document.getElementById('apiStatus').innerHTML += resultsHtml;
            }
            
            button.innerHTML = originalHtml;
            button.disabled = false;
        })
        .catch(error => {
            button.innerHTML = originalHtml;
            button.disabled = false;
            alert('Error testing API connections: ' + error.message);
        });
}

function loadDatabaseStats() {
    // This would typically fetch from your existing African meals API
    fetch('<?php echo APP_URL; ?>/api/african_meals.php?action=get_random&limit=1')
        .then(response => response.json())
        .then(data => {
            // For now, show a simple message
            document.getElementById('databaseStats').innerHTML = `
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">100+</h4>
                        <p class="text-muted">Total African Meals</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">25+</h4>
                        <p class="text-muted">Countries Covered</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">6</h4>
                        <p class="text-muted">African Regions</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">10+</h4>
                        <p class="text-muted">Emotion Transitions</p>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="<?php echo APP_URL; ?>/pages/african_meals_api_test.php" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>View Current Database
                    </a>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('databaseStats').innerHTML = `
                <div class="alert alert-info">
                    <strong>Database Status:</strong> Ready to populate with external API data
                </div>
            `;
        });
}

function populatePopularDishes() {
    const button = event.target;
    const originalHtml = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Populating...';
    button.disabled = true;
    
    addToActivityLog('Starting auto-population of popular African dishes...');
    
    fetch('<?php echo APP_URL; ?>/api/populate_african_meals.php?action=populate_popular_dishes&api=spoonacular&limit=5')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addToActivityLog(`✅ Successfully added ${data.data.total_recipes_added} recipes from ${data.data.dishes_processed} popular dishes`);
                loadDatabaseStats(); // Refresh stats
            } else {
                addToActivityLog(`❌ Failed to populate dishes: ${data.message}`);
            }
            
            button.innerHTML = originalHtml;
            button.disabled = false;
        })
        .catch(error => {
            addToActivityLog(`❌ Error during population: ${error.message}`);
            button.innerHTML = originalHtml;
            button.disabled = false;
        });
}

function searchAndAddDish() {
    const dishName = document.getElementById('dishName').value;
    const api = document.getElementById('searchApi').value;
    const sourceEmotion = document.getElementById('searchSourceEmotion').value;
    const targetEmotion = document.getElementById('searchTargetEmotion').value;
    
    if (!dishName.trim()) {
        alert('Please enter a dish name');
        return;
    }
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('searchDishModal'));
    modal.hide();
    
    addToActivityLog(`🔍 Searching for "${dishName}" using ${api} API...`);
    
    let url = `<?php echo APP_URL; ?>/api/populate_african_meals.php?action=search_and_add&dish_name=${encodeURIComponent(dishName)}&api=${api}`;
    if (sourceEmotion) url += `&source_emotion=${sourceEmotion}`;
    if (targetEmotion) url += `&target_emotion=${targetEmotion}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addToActivityLog(`✅ Found and added ${data.data.recipes_added} recipes for "${dishName}"`);
                loadDatabaseStats();
            } else {
                addToActivityLog(`❌ Failed to find recipes for "${dishName}": ${data.message}`);
            }
        })
        .catch(error => {
            addToActivityLog(`❌ Error searching for "${dishName}": ${error.message}`);
        });
    
    // Reset form
    document.getElementById('searchDishForm').reset();
}

function fetchByCuisine() {
    const cuisine = document.getElementById('cuisineType').value;
    const api = document.getElementById('fetchApi').value;
    const limit = document.getElementById('fetchLimit').value;
    const sourceEmotion = document.getElementById('fetchSourceEmotion').value;
    const targetEmotion = document.getElementById('fetchTargetEmotion').value;
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('fetchCuisineModal'));
    modal.hide();
    
    addToActivityLog(`🌍 Fetching ${limit} ${cuisine} recipes using ${api} API...`);
    
    let url = `<?php echo APP_URL; ?>/api/populate_african_meals.php?action=fetch_and_populate&cuisine=${cuisine}&api=${api}&limit=${limit}`;
    if (sourceEmotion) url += `&source_emotion=${sourceEmotion}`;
    if (targetEmotion) url += `&target_emotion=${targetEmotion}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addToActivityLog(`✅ Successfully added ${data.data.recipes_added} ${cuisine} recipes`);
                if (data.data.errors.length > 0) {
                    addToActivityLog(`⚠️ ${data.data.errors.length} errors occurred during import`);
                }
                loadDatabaseStats();
            } else {
                addToActivityLog(`❌ Failed to fetch ${cuisine} recipes: ${data.message}`);
            }
        })
        .catch(error => {
            addToActivityLog(`❌ Error fetching ${cuisine} recipes: ${error.message}`);
        });
    
    // Reset form
    document.getElementById('fetchCuisineForm').reset();
}

function addToActivityLog(message) {
    const logDiv = document.getElementById('activityLog');
    const timestamp = new Date().toLocaleTimeString();
    
    const logEntry = document.createElement('div');
    logEntry.className = 'border-bottom pb-2 mb-2';
    logEntry.innerHTML = `
        <div class="d-flex justify-content-between">
            <span>${message}</span>
            <small class="text-muted">${timestamp}</small>
        </div>
    `;
    
    // Insert at the beginning
    if (logDiv.firstChild && logDiv.firstChild.tagName !== 'P') {
        logDiv.insertBefore(logEntry, logDiv.firstChild);
    } else {
        logDiv.innerHTML = '';
        logDiv.appendChild(logEntry);
    }
    
    // Keep only last 10 entries
    while (logDiv.children.length > 10) {
        logDiv.removeChild(logDiv.lastChild);
    }
}
</script>

<?php include '../includes/footer.php'; ?>
