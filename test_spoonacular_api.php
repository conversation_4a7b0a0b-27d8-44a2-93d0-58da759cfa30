<?php
/**
 * Quick test script for Spoonacular API
 * Run this to verify your API key is working
 */

require_once 'includes/external_recipe_apis.php';

echo "<h2>Testing Spoonacular API Connection</h2>";

// Test 1: Basic API connection
echo "<h3>Test 1: Basic API Connection</h3>";
$testRecipes = fetchSpoonacularAfricanRecipes('african', 2);

if ($testRecipes) {
    echo "✅ <strong>SUCCESS!</strong> API connection is working.<br>";
    echo "Found " . count($testRecipes) . " African recipes.<br><br>";
    
    // Show first recipe details
    if (!empty($testRecipes)) {
        $recipe = $testRecipes[0];
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>Sample Recipe: " . htmlspecialchars($recipe['title']) . "</h4>";
        
        if (!empty($recipe['image'])) {
            echo "<img src='" . htmlspecialchars($recipe['image']) . "' style='max-width: 200px; height: auto; float: right; margin-left: 15px;'>";
        }
        
        echo "<p><strong>Ready in:</strong> " . ($recipe['readyInMinutes'] ?? 'Unknown') . " minutes</p>";
        echo "<p><strong>Servings:</strong> " . ($recipe['servings'] ?? 'Unknown') . "</p>";
        
        if (!empty($recipe['cuisines'])) {
            echo "<p><strong>Cuisines:</strong> " . implode(', ', $recipe['cuisines']) . "</p>";
        }
        
        if (!empty($recipe['dishTypes'])) {
            echo "<p><strong>Dish Types:</strong> " . implode(', ', $recipe['dishTypes']) . "</p>";
        }
        
        if (!empty($recipe['summary'])) {
            $summary = strip_tags($recipe['summary']);
            echo "<p><strong>Description:</strong> " . substr($summary, 0, 200) . "...</p>";
        }
        
        if (!empty($recipe['sourceUrl'])) {
            echo "<p><strong>Recipe URL:</strong> <a href='" . htmlspecialchars($recipe['sourceUrl']) . "' target='_blank'>View Full Recipe</a></p>";
        }
        
        echo "<div style='clear: both;'></div>";
        echo "</div>";
    }
} else {
    echo "❌ <strong>FAILED!</strong> Could not connect to Spoonacular API.<br>";
    echo "Please check your API key and internet connection.<br><br>";
}

// Test 2: Search for specific dish
echo "<h3>Test 2: Search for Jollof Rice</h3>";
$jollofRecipes = searchSpoonacularByName('jollof rice', 2);

if ($jollofRecipes) {
    echo "✅ <strong>SUCCESS!</strong> Found " . count($jollofRecipes) . " Jollof Rice recipes.<br><br>";
    
    foreach ($jollofRecipes as $recipe) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<strong>" . htmlspecialchars($recipe['title']) . "</strong><br>";
        if (!empty($recipe['image'])) {
            echo "<img src='" . htmlspecialchars($recipe['image']) . "' style='max-width: 150px; height: auto; float: right; margin-left: 10px;'>";
        }
        echo "Ready in: " . ($recipe['readyInMinutes'] ?? 'Unknown') . " minutes<br>";
        echo "Servings: " . ($recipe['servings'] ?? 'Unknown') . "<br>";
        if (!empty($recipe['sourceUrl'])) {
            echo "<a href='" . htmlspecialchars($recipe['sourceUrl']) . "' target='_blank'>View Recipe</a><br>";
        }
        echo "<div style='clear: both;'></div>";
        echo "</div>";
    }
} else {
    echo "❌ Could not find Jollof Rice recipes. This might be normal if the API doesn't have specific African dishes.<br><br>";
}

// Test 3: Convert to our format
echo "<h3>Test 3: Data Conversion Test</h3>";
if (!empty($testRecipes)) {
    $converted = convertExternalRecipeToOurFormat($testRecipes[0], 'spoonacular', 'sad', 'happy');
    
    echo "✅ <strong>SUCCESS!</strong> Recipe converted to our database format:<br>";
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9;'>";
    echo "<strong>Title:</strong> " . htmlspecialchars($converted['title']) . "<br>";
    echo "<strong>Description:</strong> " . htmlspecialchars($converted['description']) . "<br>";
    echo "<strong>Content:</strong> " . htmlspecialchars(substr($converted['content'], 0, 200)) . "...<br>";
    echo "<strong>Type:</strong> " . htmlspecialchars($converted['type']) . "<br>";
    echo "<strong>Source Emotion:</strong> " . htmlspecialchars($converted['source_emotion']) . "<br>";
    echo "<strong>Target Emotion:</strong> " . htmlspecialchars($converted['target_emotion']) . "<br>";
    echo "<strong>Image URL:</strong> " . htmlspecialchars($converted['image_url']) . "<br>";
    echo "<strong>External Source:</strong> " . htmlspecialchars($converted['external_source']) . "<br>";
    echo "</div>";
} else {
    echo "❌ Cannot test conversion without recipe data.<br><br>";
}

// API Usage Information
echo "<h3>API Usage Information</h3>";
echo "<div style='border: 1px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 5px; background: #e7f3ff;'>";
echo "<strong>Your Spoonacular API Key:</strong> " . SPOONACULAR_API_KEY . "<br>";
echo "<strong>API Endpoint:</strong> https://api.spoonacular.com/recipes/complexSearch<br>";
echo "<strong>Free Tier Limit:</strong> 150 points per day<br>";
echo "<strong>Points Used Per Recipe Search:</strong> ~1 point<br>";
echo "<strong>Recommended Usage:</strong> Start with 20-50 recipes to test<br>";
echo "</div>";

// Next Steps
echo "<h3>Next Steps</h3>";
echo "<div style='border: 1px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 5px; background: #e8f5e9;'>";
echo "<ol>";
echo "<li><strong>Visit the African Meals Manager:</strong> <a href='pages/african_meals_manager.php'>pages/african_meals_manager.php</a></li>";
echo "<li><strong>Test API Connection:</strong> Click 'Test API Connections' button</li>";
echo "<li><strong>Auto-Populate:</strong> Click 'Start Auto-Population' to add popular African dishes</li>";
echo "<li><strong>Search Specific Dishes:</strong> Use the search function to add dishes like 'Moroccan Tagine', 'Ethiopian Injera', etc.</li>";
echo "<li><strong>Check Results:</strong> Visit <a href='pages/african_meals_api_test.php'>pages/african_meals_api_test.php</a> to test your populated data</li>";
echo "</ol>";
echo "</div>";

echo "<h3>Popular African Dishes You Can Add</h3>";
$popularDishes = getPopularAfricanDishes();
echo "<div style='border: 1px solid #ffc107; padding: 15px; margin: 10px 0; border-radius: 5px; background: #fff8e1;'>";
echo "<p>Here are some popular African dishes you can search for and add to your database:</p>";
echo "<ul style='columns: 3; column-gap: 20px;'>";
foreach (array_slice($popularDishes, 0, 30) as $dish) {
    echo "<li>" . htmlspecialchars($dish) . "</li>";
}
echo "</ul>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h3 {
    color: #555;
    margin-top: 30px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
