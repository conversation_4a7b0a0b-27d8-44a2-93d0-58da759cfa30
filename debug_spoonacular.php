<?php
/**
 * Debug Spoonacular API responses
 */

require_once 'includes/external_recipe_apis.php';

echo "<h2>Debugging Spoonacular API</h2>";

// Test different search approaches
$testSearches = [
    'African cuisine' => ['cuisine' => 'african', 'number' => 5],
    'Moroccan cuisine' => ['cuisine' => 'moroccan', 'number' => 5],
    'Middle Eastern cuisine' => ['cuisine' => 'middle eastern', 'number' => 5],
    'Search for "tagine"' => ['query' => 'tagine', 'number' => 5],
    'Search for "couscous"' => ['query' => 'couscous', 'number' => 5],
    'Search for "jollof"' => ['query' => 'jollof', 'number' => 5],
    'Search for "moroccan chicken"' => ['query' => 'moroccan chicken', 'number' => 5]
];

foreach ($testSearches as $testName => $params) {
    echo "<h3>Testing: $testName</h3>";
    
    // Build URL
    $apiKey = SPOONACULAR_API_KEY;
    $baseParams = [
        'apiKey' => $apiKey,
        'addRecipeInformation' => 'true',
        'number' => $params['number']
    ];
    
    if (isset($params['cuisine'])) {
        $baseParams['cuisine'] = $params['cuisine'];
    }
    
    if (isset($params['query'])) {
        $baseParams['query'] = $params['query'];
    }
    
    $url = 'https://api.spoonacular.com/recipes/complexSearch?' . http_build_query($baseParams);
    
    echo "<p><strong>API URL:</strong> " . htmlspecialchars($url) . "</p>";
    
    // Make request
    $response = makeApiRequest($url);
    
    if ($response) {
        if (isset($response['results']) && !empty($response['results'])) {
            echo "<p>✅ <strong>SUCCESS!</strong> Found " . count($response['results']) . " recipes</p>";
            
            // Show first recipe
            $recipe = $response['results'][0];
            echo "<div style='border: 1px solid #28a745; padding: 10px; margin: 10px 0; background: #f8fff9;'>";
            echo "<strong>Sample Recipe:</strong> " . htmlspecialchars($recipe['title']) . "<br>";
            
            if (!empty($recipe['image'])) {
                echo "<img src='" . htmlspecialchars($recipe['image']) . "' style='max-width: 150px; height: auto; float: right; margin-left: 10px;'>";
            }
            
            if (!empty($recipe['cuisines'])) {
                echo "<strong>Cuisines:</strong> " . implode(', ', $recipe['cuisines']) . "<br>";
            }
            
            if (!empty($recipe['dishTypes'])) {
                echo "<strong>Dish Types:</strong> " . implode(', ', $recipe['dishTypes']) . "<br>";
            }
            
            echo "<strong>Ready in:</strong> " . ($recipe['readyInMinutes'] ?? 'Unknown') . " minutes<br>";
            echo "<strong>Servings:</strong> " . ($recipe['servings'] ?? 'Unknown') . "<br>";
            
            if (!empty($recipe['sourceUrl'])) {
                echo "<strong>Source:</strong> <a href='" . htmlspecialchars($recipe['sourceUrl']) . "' target='_blank'>View Recipe</a><br>";
            }
            
            echo "<div style='clear: both;'></div>";
            echo "</div>";
            
        } else {
            echo "<p>❌ <strong>NO RESULTS</strong> - API returned empty results</p>";
            echo "<pre>" . htmlspecialchars(json_encode($response, JSON_PRETTY_PRINT)) . "</pre>";
        }
    } else {
        echo "<p>❌ <strong>API ERROR</strong> - Could not get response from API</p>";
    }
    
    echo "<hr>";
}

// Test API quota
echo "<h3>API Quota Information</h3>";
$quotaUrl = "https://api.spoonacular.com/recipes/complexSearch?apiKey=" . SPOONACULAR_API_KEY . "&number=1";
$quotaResponse = makeApiRequest($quotaUrl);

if ($quotaResponse) {
    echo "<p>✅ API is responding. You still have quota remaining.</p>";
} else {
    echo "<p>❌ API might be over quota or there's a connection issue.</p>";
}

echo "<h3>Recommendations</h3>";
echo "<div style='border: 1px solid #007bff; padding: 15px; background: #f0f8ff;'>";
echo "<p><strong>Based on the results above, try these approaches:</strong></p>";
echo "<ol>";
echo "<li>If 'Moroccan cuisine' worked: Use 'moroccan' instead of 'african' in the manager</li>";
echo "<li>If 'Middle Eastern cuisine' worked: Use 'middle eastern' cuisine type</li>";
echo "<li>If specific dish searches worked: Use the 'Search Dish' function with specific names</li>";
echo "<li>If nothing worked: Your API might be over quota or there might be a connection issue</li>";
echo "</ol>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h3 {
    color: #555;
    margin-top: 30px;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
