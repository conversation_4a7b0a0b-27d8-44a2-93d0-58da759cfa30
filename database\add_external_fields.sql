-- MoodifyMe Database Schema Update
-- Add external_id and external_source columns to recommendations table for API integration

-- Use database
USE moodifyme;

-- Add external_id column if it doesn't exist
ALTER TABLE recommendations 
ADD COLUMN IF NOT EXISTS external_id VARCHAR(100) NULL AFTER link;

-- Add external_source column if it doesn't exist
ALTER TABLE recommendations 
ADD COLUMN IF NOT EXISTS external_source VARCHAR(50) NULL AFTER external_id;

-- Create index on external_id and external_source for better performance and duplicate checking
ALTER TABLE recommendations 
ADD INDEX IF NOT EXISTS idx_external_recipe (external_id, external_source);

-- Show the updated table structure
DESCRIBE recommendations;
