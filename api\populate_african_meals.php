<?php
/**
 * MoodifyMe - Populate African Meals Database
 *
 * This API endpoint fetches real African meal data from external APIs
 * and populates our database with authentic recipes
 */

// Include configuration and functions
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/db_connect.php';
require_once '../includes/external_recipe_apis.php';

// Start session
session_start();

// Set response header
header('Content-Type: application/json');

// Check if user is admin (you might want to add admin role checking)
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Authentication required',
        'error_code' => 'AUTH_REQUIRED'
    ]);
    exit;
}

// Get action parameter
$action = $_GET['action'] ?? '';

// Route to appropriate handler
switch ($action) {
    case 'fetch_and_populate':
        handleFetchAndPopulate();
        break;
    case 'search_and_add':
        handleSearchAndAdd();
        break;
    case 'populate_popular_dishes':
        handlePopulatePopularDishes();
        break;
    case 'test_api_connection':
        handleTestApiConnection();
        break;
    case 'get_api_status':
        handleGetApiStatus();
        break;
    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action specified',
            'error_code' => 'INVALID_ACTION',
            'available_actions' => [
                'fetch_and_populate', 'search_and_add', 'populate_popular_dishes',
                'test_api_connection', 'get_api_status'
            ]
        ]);
        break;
}

/**
 * Fetch recipes from external APIs and populate database
 */
function handleFetchAndPopulate() {
    $api = $_GET['api'] ?? 'spoonacular'; // spoonacular or edamam
    $cuisine = $_GET['cuisine'] ?? 'african';
    $limit = min(intval($_GET['limit'] ?? 10), 50);
    $sourceEmotion = $_GET['source_emotion'] ?? '';
    $targetEmotion = $_GET['target_emotion'] ?? '';

    $recipes = [];
    $errors = [];

    try {
        // Fetch recipes from external API
        if ($api === 'spoonacular') {
            $externalRecipes = fetchSpoonacularAfricanRecipes($cuisine, $limit);
        } elseif ($api === 'edamam') {
            $externalRecipes = fetchEdamamAfricanRecipes($cuisine, 0, $limit);
        } else {
            throw new Exception('Invalid API specified');
        }

        if (!$externalRecipes) {
            throw new Exception('Failed to fetch recipes from external API');
        }

        // Convert and save to database
        foreach ($externalRecipes as $externalRecipe) {
            try {
                $formattedRecipe = convertExternalRecipeToOurFormat(
                    $externalRecipe,
                    $api,
                    $sourceEmotion,
                    $targetEmotion
                );

                $recipeId = saveRecipeToDatabase($formattedRecipe);

                if ($recipeId) {
                    $recipes[] = [
                        'id' => $recipeId,
                        'title' => $formattedRecipe['title'],
                        'external_id' => $formattedRecipe['external_id'],
                        'source' => $api
                    ];
                } else {
                    $errors[] = "Failed to save recipe: " . $formattedRecipe['title'];
                }
            } catch (Exception $e) {
                $errors[] = "Error processing recipe: " . $e->getMessage();
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Recipes fetched and saved successfully',
            'data' => [
                'recipes_added' => count($recipes),
                'recipes' => $recipes,
                'errors' => $errors,
                'api_used' => $api,
                'cuisine' => $cuisine
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to fetch and populate recipes: ' . $e->getMessage(),
            'error_code' => 'FETCH_FAILED'
        ]);
    }
}

/**
 * Search for specific dish and add to database
 */
function handleSearchAndAdd() {
    $dishName = $_GET['dish_name'] ?? '';
    $api = $_GET['api'] ?? 'spoonacular';
    $sourceEmotion = $_GET['source_emotion'] ?? '';
    $targetEmotion = $_GET['target_emotion'] ?? '';

    if (empty($dishName)) {
        echo json_encode([
            'success' => false,
            'message' => 'Dish name is required',
            'error_code' => 'MISSING_DISH_NAME'
        ]);
        return;
    }

    try {
        $externalRecipes = searchAfricanDishByName($dishName, $api, 5);

        if (!$externalRecipes) {
            throw new Exception('No recipes found for the specified dish');
        }

        $recipes = [];
        $errors = [];

        foreach ($externalRecipes as $externalRecipe) {
            try {
                $formattedRecipe = convertExternalRecipeToOurFormat(
                    $externalRecipe,
                    $api,
                    $sourceEmotion,
                    $targetEmotion
                );

                $recipeId = saveRecipeToDatabase($formattedRecipe);

                if ($recipeId) {
                    $recipes[] = [
                        'id' => $recipeId,
                        'title' => $formattedRecipe['title'],
                        'external_id' => $formattedRecipe['external_id']
                    ];
                }
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Dish recipes found and added',
            'data' => [
                'dish_searched' => $dishName,
                'recipes_added' => count($recipes),
                'recipes' => $recipes,
                'errors' => $errors
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to search and add dish: ' . $e->getMessage(),
            'error_code' => 'SEARCH_FAILED'
        ]);
    }
}

/**
 * Populate database with popular African dishes
 */
function handlePopulatePopularDishes() {
    $api = $_GET['api'] ?? 'spoonacular';
    $limit = min(intval($_GET['limit'] ?? 5), 10); // Limit dishes to avoid API quota

    $popularDishes = getPopularAfricanDishes();
    $selectedDishes = array_slice($popularDishes, 0, $limit);

    $results = [];
    $totalAdded = 0;

    foreach ($selectedDishes as $dish) {
        try {
            $externalRecipes = searchAfricanDishByName($dish, $api, 2); // 2 recipes per dish

            if ($externalRecipes) {
                $dishResults = [];

                foreach ($externalRecipes as $externalRecipe) {
                    try {
                        // Auto-assign emotions based on dish characteristics
                        $emotions = assignEmotionsBasedOnDish($dish);

                        $formattedRecipe = convertExternalRecipeToOurFormat(
                            $externalRecipe,
                            $api,
                            $emotions['source'],
                            $emotions['target']
                        );

                        $recipeId = saveRecipeToDatabase($formattedRecipe);

                        if ($recipeId) {
                            $dishResults[] = [
                                'id' => $recipeId,
                                'title' => $formattedRecipe['title']
                            ];
                            $totalAdded++;
                        }
                    } catch (Exception $e) {
                        // Continue with next recipe
                    }
                }

                $results[$dish] = $dishResults;
            }

            // Small delay to respect API rate limits
            usleep(500000); // 0.5 second delay

        } catch (Exception $e) {
            $results[$dish] = ['error' => $e->getMessage()];
        }
    }

    echo json_encode([
        'success' => true,
        'message' => 'Popular African dishes populated',
        'data' => [
            'total_recipes_added' => $totalAdded,
            'dishes_processed' => count($selectedDishes),
            'results' => $results,
            'api_used' => $api
        ]
    ]);
}

/**
 * Test API connection
 */
function handleTestApiConnection() {
    $api = $_GET['api'] ?? 'both';

    $results = [];

    if ($api === 'spoonacular' || $api === 'both') {
        $spoonacularTest = fetchSpoonacularAfricanRecipes('african', 1);
        $results['spoonacular'] = [
            'status' => $spoonacularTest ? 'connected' : 'failed',
            'message' => $spoonacularTest ? 'API connection successful' : 'API connection failed - check API key'
        ];
    }

    if ($api === 'edamam' || $api === 'both') {
        $edamamTest = fetchEdamamAfricanRecipes('African', 0, 1);
        $results['edamam'] = [
            'status' => $edamamTest ? 'connected' : 'failed',
            'message' => $edamamTest ? 'API connection successful' : 'API connection failed - check credentials'
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $results
    ]);
}

/**
 * Get API status and configuration
 */
function handleGetApiStatus() {
    $status = [
        'spoonacular' => [
            'configured' => SPOONACULAR_API_KEY !== 'your_spoonacular_api_key_here',
            'api_key_set' => !empty(SPOONACULAR_API_KEY),
            'endpoint' => 'https://api.spoonacular.com/recipes/complexSearch'
        ],
        'edamam' => [
            'configured' => EDAMAM_RECIPE_APP_ID !== 'your_edamam_app_id_here',
            'app_id_set' => !empty(EDAMAM_RECIPE_APP_ID),
            'app_key_set' => !empty(EDAMAM_RECIPE_APP_KEY),
            'endpoint' => 'https://api.edamam.com/api/recipes/v2'
        ]
    ];

    echo json_encode([
        'success' => true,
        'data' => $status
    ]);
}

/**
 * Save recipe to database
 */
function saveRecipeToDatabase($recipe) {
    global $conn;

    // Check if recipe already exists (by external_id)
    if (!empty($recipe['external_id'])) {
        $stmt = $conn->prepare("SELECT id FROM recommendations WHERE external_id = ? AND external_source = ?");
        $stmt->bind_param("ss", $recipe['external_id'], $recipe['external_source']);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Recipe already exists
            return false;
        }
    }

    $stmt = $conn->prepare("
        INSERT INTO recommendations (
            title, description, type, source_emotion, target_emotion,
            content, image_url, link, external_id, external_source, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->bind_param(
        "sssssssssss",
        $recipe['title'],
        $recipe['description'],
        $recipe['type'],
        $recipe['source_emotion'],
        $recipe['target_emotion'],
        $recipe['content'],
        $recipe['image_url'],
        $recipe['link'],
        $recipe['external_id'],
        $recipe['external_source'],
        $recipe['created_at']
    );

    if ($stmt->execute()) {
        return $conn->insert_id;
    }

    return false;
}

/**
 * Assign emotions based on dish characteristics
 */
function assignEmotionsBasedOnDish($dish) {
    $dish = strtolower($dish);

    // Comfort foods: sad -> happy
    $comfortFoods = ['stew', 'soup', 'tagine', 'doro wat', 'moambe'];

    // Spicy foods: bored -> excited
    $spicyFoods = ['suya', 'berbere', 'harissa', 'pepper soup'];

    // Light foods: stressed -> calm
    $lightFoods = ['couscous', 'injera', 'ugali', 'fufu'];

    // Festive foods: neutral -> happy
    $festiveFoods = ['jollof', 'biryani', 'pastilla', 'bobotie'];

    foreach ($comfortFoods as $food) {
        if (strpos($dish, $food) !== false) {
            return ['source' => 'sad', 'target' => 'happy'];
        }
    }

    foreach ($spicyFoods as $food) {
        if (strpos($dish, $food) !== false) {
            return ['source' => 'bored', 'target' => 'excited'];
        }
    }

    foreach ($lightFoods as $food) {
        if (strpos($dish, $food) !== false) {
            return ['source' => 'stressed', 'target' => 'calm'];
        }
    }

    foreach ($festiveFoods as $food) {
        if (strpos($dish, $food) !== false) {
            return ['source' => 'neutral', 'target' => 'happy'];
        }
    }

    // Default mapping
    return ['source' => 'neutral', 'target' => 'happy'];
}

?>
