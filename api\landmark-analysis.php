<?php
/**
 * MoodifyMe - Landmark-Based Emotion Analysis API
 * Processes landmark-based emotion detection results
 */

require_once '../config.php';
require_once '../includes/auth_check.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON input'
    ]);
    exit;
}

// Validate required fields
$requiredFields = ['emotion', 'confidence', 'landmarks'];
foreach ($requiredFields as $field) {
    if (!isset($input[$field])) {
        echo json_encode([
            'success' => false,
            'message' => "Missing required field: $field"
        ]);
        exit;
    }
}

$emotion = $input['emotion'];
$confidence = floatval($input['confidence']);
$landmarks = $input['landmarks'];
$userId = $_SESSION['user_id'];

// Validate emotion
$validEmotions = ['happy', 'sad', 'angry', 'surprised', 'fear', 'disgust', 'neutral'];
if (!in_array($emotion, $validEmotions)) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid emotion detected'
    ]);
    exit;
}

// Validate confidence
if ($confidence < 0 || $confidence > 1) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid confidence value'
    ]);
    exit;
}

try {
    // Log the landmark-based emotion detection
    $stmt = $pdo->prepare("
        INSERT INTO emotion_logs (user_id, emotion, confidence, input_type, input_data, created_at) 
        VALUES (?, ?, ?, 'landmark', ?, NOW())
    ");
    
    $landmarkData = json_encode([
        'method' => 'mediapipe_landmarks',
        'landmark_count' => count($landmarks),
        'features_analyzed' => [
            'mouth_curvature',
            'eye_aspect_ratio',
            'eyebrow_height',
            'facial_symmetry'
        ],
        'processing_time' => $input['processing_time'] ?? null,
        'face_detected' => true
    ]);
    
    $stmt->execute([$userId, $emotion, $confidence, $landmarkData]);
    $emotionId = $pdo->lastInsertId();

    // Get emotion category for recommendations
    $emotionCategories = [
        'happy' => 'positive',
        'sad' => 'negative', 
        'angry' => 'negative',
        'surprised' => 'neutral',
        'fear' => 'negative',
        'disgust' => 'negative',
        'neutral' => 'neutral'
    ];

    $category = $emotionCategories[$emotion] ?? 'neutral';

    // Analyze landmark quality
    $landmarkQuality = analyzeLandmarkQuality($landmarks);
    
    // Generate insights based on landmarks
    $insights = generateLandmarkInsights($emotion, $confidence, $landmarkQuality);

    // Prepare response
    $response = [
        'success' => true,
        'emotion' => $emotion,
        'confidence' => $confidence,
        'emotion_id' => $emotionId,
        'category' => $category,
        'method' => 'landmark_detection',
        'landmark_quality' => $landmarkQuality,
        'insights' => $insights,
        'recommendations' => [
            'continue_to_mood_options' => true,
            'suggested_target_emotions' => getSuggestedTargetEmotions($emotion),
            'confidence_threshold_met' => $confidence >= 0.3
        ]
    ];

    // Add clarification if confidence is low
    if ($confidence < 0.5) {
        $response['needs_clarification'] = true;
        $response['clarification_message'] = "Emotion detected with moderate confidence. You may want to try again for better accuracy.";
    }

    echo json_encode($response);

} catch (PDOException $e) {
    error_log("Database error in landmark analysis: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}

/**
 * Analyze the quality of landmark detection
 */
function analyzeLandmarkQuality($landmarks) {
    $quality = [
        'total_landmarks' => count($landmarks),
        'face_coverage' => 'good',
        'symmetry_score' => 0.85,
        'detection_stability' => 'stable'
    ];

    // Basic quality checks
    if (count($landmarks) < 400) {
        $quality['face_coverage'] = 'partial';
    }

    // Check for landmark distribution (simplified)
    $xCoords = array_column($landmarks, 'x');
    $yCoords = array_column($landmarks, 'y');
    
    $xRange = max($xCoords) - min($xCoords);
    $yRange = max($yCoords) - min($yCoords);
    
    if ($xRange < 0.3 || $yRange < 0.3) {
        $quality['face_coverage'] = 'limited';
    }

    return $quality;
}

/**
 * Generate insights based on landmark analysis
 */
function generateLandmarkInsights($emotion, $confidence, $quality) {
    $insights = [];

    // Confidence-based insights
    if ($confidence >= 0.8) {
        $insights[] = "High confidence detection - your facial expression clearly indicates $emotion.";
    } elseif ($confidence >= 0.6) {
        $insights[] = "Good detection quality - $emotion emotion identified with reasonable confidence.";
    } else {
        $insights[] = "Moderate confidence - consider adjusting lighting or camera angle for better detection.";
    }

    // Quality-based insights
    if ($quality['face_coverage'] === 'good') {
        $insights[] = "Excellent face coverage detected with " . $quality['total_landmarks'] . " landmarks.";
    } else {
        $insights[] = "Partial face detection - try positioning your face more centrally in the camera.";
    }

    // Emotion-specific insights
    switch ($emotion) {
        case 'happy':
            $insights[] = "Positive facial expression detected - smile and eye patterns indicate happiness.";
            break;
        case 'sad':
            $insights[] = "Downward facial features suggest sadness - consider mood-lifting activities.";
            break;
        case 'angry':
            $insights[] = "Tense facial muscles detected - relaxation techniques might be helpful.";
            break;
        case 'surprised':
            $insights[] = "Wide eyes and raised eyebrows indicate surprise or alertness.";
            break;
        case 'neutral':
            $insights[] = "Balanced facial expression - you appear calm and composed.";
            break;
    }

    return $insights;
}

/**
 * Get suggested target emotions based on current emotion
 */
function getSuggestedTargetEmotions($currentEmotion) {
    $suggestions = [
        'happy' => ['energetic', 'calm', 'inspired'],
        'sad' => ['happy', 'calm', 'hopeful'],
        'angry' => ['calm', 'peaceful', 'relaxed'],
        'surprised' => ['calm', 'focused', 'relaxed'],
        'fear' => ['calm', 'confident', 'secure'],
        'disgust' => ['neutral', 'calm', 'positive'],
        'neutral' => ['happy', 'energetic', 'inspired']
    ];

    return $suggestions[$currentEmotion] ?? ['happy', 'calm', 'relaxed'];
}
?>
