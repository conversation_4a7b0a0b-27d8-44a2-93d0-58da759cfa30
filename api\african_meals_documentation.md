# African Meals API Documentation

## Overview
The African Meals API provides comprehensive access to African meal recommendations based on mood transitions, regions, countries, and search functionality.

## Base URL
```
/api/african_meals.php
```

## Authentication
Most endpoints require user authentication via session. Public endpoints include:
- `get_random`
- `search`
- `get_by_region`
- `get_by_country`

## Endpoints

### 1. Get Meals by Mood Transition
Get African meals that help transition from one emotion to another.

**Endpoint:** `GET /api/african_meals.php?action=get_by_mood`

**Parameters:**
- `source` (required): Source emotion (e.g., "sad", "angry", "anxious")
- `target` (required): Target emotion (e.g., "happy", "calm", "excited")
- `limit` (optional): Number of results (default: 10, max: 50)
- `page` (optional): Page number for pagination (default: 1)

**Example:**
```
GET /api/african_meals.php?action=get_by_mood&source=sad&target=happy&limit=5
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "Comforting Ethiopian Doro Wat",
      "description": "A hearty, spicy chicken stew to warm your spirits",
      "content": "A rich, spicy chicken stew served with injera bread...",
      "image_url": "https://yoursite.com/assets/images/doro_wat.jpg",
      "link": "https://recipe-link.com",
      "likes": 15,
      "dislikes": 2,
      "user_feedback": "like",
      "country": "Ethiopia",
      "region": "East Africa",
      "difficulty": "Medium",
      "cooking_time": "2+ hours",
      "dietary_tags": ["High Protein", "Spicy", "Comfort Food"]
    }
  ],
  "meta": {
    "source_emotion": "sad",
    "target_emotion": "happy",
    "page": 1,
    "limit": 5,
    "total_results": 5
  }
}
```

### 2. Get Meals by Region
Get African meals from a specific region.

**Endpoint:** `GET /api/african_meals.php?action=get_by_region`

**Parameters:**
- `region` (required): African region
  - `west_africa`
  - `east_africa`
  - `north_africa`
  - `south_africa`
  - `central_africa`
  - `island_africa`
- `limit` (optional): Number of results (default: 20, max: 50)
- `page` (optional): Page number for pagination (default: 1)

**Example:**
```
GET /api/african_meals.php?action=get_by_region&region=west_africa&limit=10
```

### 3. Get Meals by Country
Get African meals from a specific country.

**Endpoint:** `GET /api/african_meals.php?action=get_by_country`

**Parameters:**
- `country` (required): Country name (e.g., "Nigeria", "Ethiopia", "Morocco")
- `limit` (optional): Number of results (default: 20, max: 50)
- `page` (optional): Page number for pagination (default: 1)

**Example:**
```
GET /api/african_meals.php?action=get_by_country&country=Nigeria&limit=10
```

### 4. Search Meals
Search African meals by title, description, or content.

**Endpoint:** `GET /api/african_meals.php?action=search`

**Parameters:**
- `query` (required): Search term (minimum 2 characters)
- `limit` (optional): Number of results (default: 20, max: 50)
- `page` (optional): Page number for pagination (default: 1)

**Example:**
```
GET /api/african_meals.php?action=search&query=jollof&limit=10
```

### 5. Get Specific Meal
Get detailed information about a specific meal.

**Endpoint:** `GET /api/african_meals.php?action=get_meal`

**Parameters:**
- `id` (required): Meal ID

**Example:**
```
GET /api/african_meals.php?action=get_meal&id=123
```

### 6. Get Popular Meals
Get the most liked African meals.

**Endpoint:** `GET /api/african_meals.php?action=get_popular`

**Parameters:**
- `limit` (optional): Number of results (default: 10, max: 50)
- `timeframe` (optional): Time period for popularity calculation
  - `all_time` (default)
  - `month`
  - `week`

**Example:**
```
GET /api/african_meals.php?action=get_popular&limit=10&timeframe=month
```

### 7. Get Random Meals
Get random African meals for discovery.

**Endpoint:** `GET /api/african_meals.php?action=get_random`

**Parameters:**
- `limit` (optional): Number of results (default: 5, max: 20)

**Example:**
```
GET /api/african_meals.php?action=get_random&limit=5
```

### 8. Add Feedback
Add like/dislike feedback for a meal (requires authentication).

**Endpoint:** `POST /api/african_meals.php?action=add_feedback`

**Request Body:**
```json
{
  "meal_id": 123,
  "feedback_type": "like"
}
```

**Parameters:**
- `meal_id` (required): ID of the meal
- `feedback_type` (required): Either "like" or "dislike"

**Response:**
```json
{
  "success": true,
  "message": "Feedback recorded successfully",
  "data": {
    "meal_id": 123,
    "user_feedback": "like",
    "total_likes": 16,
    "total_dislikes": 2
  }
}
```

### 9. Get Available Regions
Get list of available African regions.

**Endpoint:** `GET /api/african_meals.php?action=get_regions`

**Response:**
```json
{
  "success": true,
  "data": {
    "west_africa": "West Africa",
    "east_africa": "East Africa",
    "north_africa": "North Africa",
    "south_africa": "Southern Africa",
    "central_africa": "Central Africa",
    "island_africa": "African Islands"
  }
}
```

### 10. Get Available Countries
Get list of countries that have meals in the database.

**Endpoint:** `GET /api/african_meals.php?action=get_countries`

**Response:**
```json
{
  "success": true,
  "data": [
    "Nigeria",
    "Ghana",
    "Ethiopia",
    "Morocco",
    "Egypt",
    "Kenya"
  ],
  "meta": {
    "total_countries": 25
  }
}
```

## Response Format

### Success Response
```json
{
  "success": true,
  "data": [...],
  "meta": {
    "page": 1,
    "limit": 10,
    "total_results": 25
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE"
}
```

## Error Codes
- `AUTH_REQUIRED`: Authentication required for this endpoint
- `INVALID_ACTION`: Invalid action parameter
- `MISSING_EMOTIONS`: Source and target emotions required
- `MISSING_REGION`: Region parameter required
- `MISSING_COUNTRY`: Country parameter required
- `INVALID_QUERY`: Search query too short or invalid
- `INVALID_MEAL_ID`: Invalid or missing meal ID
- `MEAL_NOT_FOUND`: Meal not found
- `INVALID_METHOD`: Wrong HTTP method
- `INVALID_FEEDBACK_TYPE`: Feedback type must be "like" or "dislike"
- `FEEDBACK_FAILED`: Failed to record feedback

## Data Fields

### Meal Object
- `id`: Unique meal identifier
- `title`: Meal name
- `description`: Brief description
- `content`: Detailed information about the meal
- `image_url`: URL to meal image
- `link`: External recipe link
- `likes`: Number of likes
- `dislikes`: Number of dislikes
- `user_feedback`: Current user's feedback ("like", "dislike", or null)
- `country`: Country of origin
- `region`: African region
- `difficulty`: Cooking difficulty ("Easy", "Medium", "Hard")
- `cooking_time`: Estimated cooking time
- `dietary_tags`: Array of dietary tags (e.g., ["Vegetarian", "Spicy"])

## Rate Limiting
No rate limiting is currently implemented, but it's recommended to make reasonable requests.

## CORS
The API supports CORS for cross-origin requests.
