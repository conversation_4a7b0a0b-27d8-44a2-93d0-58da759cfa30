<?php
/**
 * MoodifyMe - African Meals API Test Page
 * Test page to demonstrate the African Meals API functionality
 */

// Include configuration and functions
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/db_connect.php';

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    redirect(APP_URL . '/pages/login.php');
}

// Include header
include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title mb-0">
                        <i class="fas fa-utensils me-2"></i>
                        African Meals API Test Page
                    </h2>
                </div>
                <div class="card-body">
                    <p class="lead">Test the various endpoints of the African Meals API</p>
                    
                    <!-- API Test Controls -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Test Controls</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="apiAction" class="form-label">API Action</label>
                                        <select class="form-select" id="apiAction">
                                            <option value="get_by_mood">Get by Mood</option>
                                            <option value="get_by_region">Get by Region</option>
                                            <option value="get_by_country">Get by Country</option>
                                            <option value="search">Search</option>
                                            <option value="get_popular">Get Popular</option>
                                            <option value="get_random">Get Random</option>
                                            <option value="get_regions">Get Regions</option>
                                            <option value="get_countries">Get Countries</option>
                                        </select>
                                    </div>
                                    
                                    <!-- Mood Parameters -->
                                    <div id="moodParams" class="mood-params">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label for="sourceEmotion" class="form-label">Source Emotion</label>
                                                <select class="form-select" id="sourceEmotion">
                                                    <option value="sad">Sad</option>
                                                    <option value="angry">Angry</option>
                                                    <option value="anxious">Anxious</option>
                                                    <option value="stressed">Stressed</option>
                                                    <option value="bored">Bored</option>
                                                    <option value="tired">Tired</option>
                                                    <option value="neutral">Neutral</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="targetEmotion" class="form-label">Target Emotion</label>
                                                <select class="form-select" id="targetEmotion">
                                                    <option value="happy">Happy</option>
                                                    <option value="calm">Calm</option>
                                                    <option value="excited">Excited</option>
                                                    <option value="energetic">Energetic</option>
                                                    <option value="focused">Focused</option>
                                                    <option value="relaxed">Relaxed</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Region Parameter -->
                                    <div id="regionParam" class="region-param" style="display: none;">
                                        <label for="region" class="form-label">Region</label>
                                        <select class="form-select" id="region">
                                            <option value="west_africa">West Africa</option>
                                            <option value="east_africa">East Africa</option>
                                            <option value="north_africa">North Africa</option>
                                            <option value="south_africa">Southern Africa</option>
                                            <option value="central_africa">Central Africa</option>
                                            <option value="island_africa">African Islands</option>
                                        </select>
                                    </div>
                                    
                                    <!-- Country Parameter -->
                                    <div id="countryParam" class="country-param" style="display: none;">
                                        <label for="country" class="form-label">Country</label>
                                        <input type="text" class="form-control" id="country" placeholder="e.g., Nigeria, Ethiopia, Morocco">
                                    </div>
                                    
                                    <!-- Search Parameter -->
                                    <div id="searchParam" class="search-param" style="display: none;">
                                        <label for="searchQuery" class="form-label">Search Query</label>
                                        <input type="text" class="form-control" id="searchQuery" placeholder="e.g., jollof, stew, rice">
                                    </div>
                                    
                                    <!-- Popular Parameters -->
                                    <div id="popularParams" class="popular-params" style="display: none;">
                                        <label for="timeframe" class="form-label">Timeframe</label>
                                        <select class="form-select" id="timeframe">
                                            <option value="all_time">All Time</option>
                                            <option value="month">This Month</option>
                                            <option value="week">This Week</option>
                                        </select>
                                    </div>
                                    
                                    <!-- Common Parameters -->
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <label for="limit" class="form-label">Limit</label>
                                            <input type="number" class="form-control" id="limit" value="10" min="1" max="50">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="page" class="form-label">Page</label>
                                            <input type="number" class="form-control" id="page" value="1" min="1">
                                        </div>
                                    </div>
                                    
                                    <button type="button" class="btn btn-primary mt-3 w-100" onclick="testAPI()">
                                        <i class="fas fa-play me-2"></i>Test API
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>API URL</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Generated URL:</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="generatedUrl" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyUrl()">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Response Status:</label>
                                        <span id="responseStatus" class="badge bg-secondary">Not tested</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Response Time:</label>
                                        <span id="responseTime" class="text-muted">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Results Section -->
                    <div class="card">
                        <div class="card-header">
                            <h5>API Response</h5>
                        </div>
                        <div class="card-body">
                            <div id="loadingIndicator" style="display: none;" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Testing API...</p>
                            </div>
                            
                            <div id="apiResults">
                                <p class="text-muted">Click "Test API" to see results here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update parameter visibility based on selected action
document.getElementById('apiAction').addEventListener('change', function() {
    const action = this.value;
    
    // Hide all parameter sections
    document.querySelectorAll('.mood-params, .region-param, .country-param, .search-param, .popular-params').forEach(el => {
        el.style.display = 'none';
    });
    
    // Show relevant parameters
    switch(action) {
        case 'get_by_mood':
            document.querySelector('.mood-params').style.display = 'block';
            break;
        case 'get_by_region':
            document.querySelector('.region-param').style.display = 'block';
            break;
        case 'get_by_country':
            document.querySelector('.country-param').style.display = 'block';
            break;
        case 'search':
            document.querySelector('.search-param').style.display = 'block';
            break;
        case 'get_popular':
            document.querySelector('.popular-params').style.display = 'block';
            break;
    }
    
    updateGeneratedUrl();
});

// Update generated URL when parameters change
document.querySelectorAll('#apiAction, #sourceEmotion, #targetEmotion, #region, #country, #searchQuery, #timeframe, #limit, #page').forEach(el => {
    el.addEventListener('change', updateGeneratedUrl);
    el.addEventListener('input', updateGeneratedUrl);
});

function updateGeneratedUrl() {
    const action = document.getElementById('apiAction').value;
    const baseUrl = '<?php echo APP_URL; ?>/api/african_meals.php';
    let url = `${baseUrl}?action=${action}`;
    
    // Add parameters based on action
    switch(action) {
        case 'get_by_mood':
            url += `&source=${document.getElementById('sourceEmotion').value}`;
            url += `&target=${document.getElementById('targetEmotion').value}`;
            break;
        case 'get_by_region':
            url += `&region=${document.getElementById('region').value}`;
            break;
        case 'get_by_country':
            const country = document.getElementById('country').value;
            if (country) url += `&country=${encodeURIComponent(country)}`;
            break;
        case 'search':
            const query = document.getElementById('searchQuery').value;
            if (query) url += `&query=${encodeURIComponent(query)}`;
            break;
        case 'get_popular':
            url += `&timeframe=${document.getElementById('timeframe').value}`;
            break;
    }
    
    // Add common parameters
    if (['get_by_mood', 'get_by_region', 'get_by_country', 'search', 'get_popular', 'get_random'].includes(action)) {
        const limit = document.getElementById('limit').value;
        const page = document.getElementById('page').value;
        if (limit && limit !== '10') url += `&limit=${limit}`;
        if (page && page !== '1') url += `&page=${page}`;
    }
    
    document.getElementById('generatedUrl').value = url;
}

function testAPI() {
    const url = document.getElementById('generatedUrl').value;
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsDiv = document.getElementById('apiResults');
    const statusSpan = document.getElementById('responseStatus');
    const timeSpan = document.getElementById('responseTime');
    
    // Show loading
    loadingIndicator.style.display = 'block';
    resultsDiv.innerHTML = '';
    statusSpan.textContent = 'Loading...';
    statusSpan.className = 'badge bg-warning';
    
    const startTime = Date.now();
    
    fetch(url)
        .then(response => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            timeSpan.textContent = `${responseTime}ms`;
            
            if (response.ok) {
                statusSpan.textContent = `${response.status} OK`;
                statusSpan.className = 'badge bg-success';
            } else {
                statusSpan.textContent = `${response.status} Error`;
                statusSpan.className = 'badge bg-danger';
            }
            
            return response.json();
        })
        .then(data => {
            loadingIndicator.style.display = 'none';
            
            // Display formatted JSON
            resultsDiv.innerHTML = `
                <div class="mb-3">
                    <strong>Success:</strong> ${data.success ? 'Yes' : 'No'}
                </div>
                <pre class="bg-light p-3 rounded"><code>${JSON.stringify(data, null, 2)}</code></pre>
            `;
            
            // If there are meals, show them in a nice format
            if (data.success && data.data && Array.isArray(data.data) && data.data.length > 0) {
                const mealsHtml = data.data.map(meal => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <h6 class="card-title">${meal.title}</h6>
                            <p class="card-text small">${meal.description}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${meal.country} - ${meal.region}</small>
                                <div>
                                    <span class="badge bg-primary">${meal.difficulty}</span>
                                    <span class="badge bg-info">${meal.cooking_time}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
                
                resultsDiv.innerHTML += `
                    <div class="mt-4">
                        <h6>Meals Preview:</h6>
                        ${mealsHtml}
                    </div>
                `;
            }
        })
        .catch(error => {
            loadingIndicator.style.display = 'none';
            statusSpan.textContent = 'Error';
            statusSpan.className = 'badge bg-danger';
            timeSpan.textContent = '-';
            
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error:</strong> ${error.message}
                </div>
            `;
        });
}

function copyUrl() {
    const urlInput = document.getElementById('generatedUrl');
    urlInput.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    setTimeout(() => {
        button.innerHTML = originalHtml;
    }, 1000);
}

// Initialize
updateGeneratedUrl();
</script>

<?php include '../includes/footer.php'; ?>
